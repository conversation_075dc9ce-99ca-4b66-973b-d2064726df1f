
/* autogenerated by generate-bytecode */

#include <math.h>
#include <orc/orc.h>

typedef enum {
  ORC_BC_END,
  ORC_BC_BEGIN_FUNCTION,
  ORC_BC_END_FUNCTION,
  ORC_BC_SET_CONSTANT_N,
  ORC_BC_SET_N_MULTIPLE,
  ORC_BC_SET_N_MINIMUM,
  ORC_BC_SET_N_MAXIMUM,
  ORC_BC_SET_2D,
  ORC_BC_SET_CONSTANT_M,
  ORC_BC_SET_NAME,
  ORC_BC_SET_BACKUP_FUNCTION,
  ORC_BC_ADD_DESTINATION,
  ORC_BC_ADD_SOURCE,
  ORC_BC_ADD_ACCUMULATOR,
  ORC_BC_ADD_CONSTANT,
  ORC_BC_ADD_CONSTANT_INT64,
  ORC_BC_ADD_PARAMETER,
  ORC_BC_ADD_PARAMETER_FLOAT,
  ORC_BC_ADD_PARAMETER_INT64,
  ORC_BC_ADD_PARAMETER_DOUBLE,
  ORC_<PERSON>_ADD_TEMPORARY,
  ORC_BC_INSTRUCTION_FLAGS,
  ORC_BC_RESERVED_22,
  ORC_BC_RESERVED_23,
  ORC_BC_RESERVED_24,
  ORC_BC_RESERVED_25,
  ORC_BC_RESERVED_26,
  ORC_BC_RESERVED_27,
  ORC_BC_RESERVED_28,
  ORC_BC_RESERVED_29,
  ORC_BC_RESERVED_30,
  ORC_BC_RESERVED_31,
  ORC_BC_absb,
  ORC_BC_addb,
  ORC_BC_addssb,
  ORC_BC_addusb,
  ORC_BC_andb,
  ORC_BC_andnb,
  ORC_BC_avgsb,
  ORC_BC_avgub,
  /* 40 */
  ORC_BC_cmpeqb,
  ORC_BC_cmpgtsb,
  ORC_BC_copyb,
  ORC_BC_loadb,
  ORC_BC_loadoffb,
  ORC_BC_loadupdb,
  ORC_BC_loadupib,
  ORC_BC_loadpb,
  ORC_BC_ldresnearb,
  ORC_BC_ldresnearl,
  /* 50 */
  ORC_BC_ldreslinb,
  ORC_BC_ldreslinl,
  ORC_BC_maxsb,
  ORC_BC_maxub,
  ORC_BC_minsb,
  ORC_BC_minub,
  ORC_BC_mullb,
  ORC_BC_mulhsb,
  ORC_BC_mulhub,
  ORC_BC_orb,
  /* 60 */
  ORC_BC_shlb,
  ORC_BC_shrsb,
  ORC_BC_shrub,
  ORC_BC_signb,
  ORC_BC_storeb,
  ORC_BC_subb,
  ORC_BC_subssb,
  ORC_BC_subusb,
  ORC_BC_xorb,
  ORC_BC_absw,
  /* 70 */
  ORC_BC_addw,
  ORC_BC_addssw,
  ORC_BC_addusw,
  ORC_BC_andw,
  ORC_BC_andnw,
  ORC_BC_avgsw,
  ORC_BC_avguw,
  ORC_BC_cmpeqw,
  ORC_BC_cmpgtsw,
  ORC_BC_copyw,
  /* 80 */
  ORC_BC_div255w,
  ORC_BC_divluw,
  ORC_BC_loadw,
  ORC_BC_loadoffw,
  ORC_BC_loadpw,
  ORC_BC_maxsw,
  ORC_BC_maxuw,
  ORC_BC_minsw,
  ORC_BC_minuw,
  ORC_BC_mullw,
  /* 90 */
  ORC_BC_mulhsw,
  ORC_BC_mulhuw,
  ORC_BC_orw,
  ORC_BC_shlw,
  ORC_BC_shrsw,
  ORC_BC_shruw,
  ORC_BC_signw,
  ORC_BC_storew,
  ORC_BC_subw,
  ORC_BC_subssw,
  /* 100 */
  ORC_BC_subusw,
  ORC_BC_xorw,
  ORC_BC_absl,
  ORC_BC_addl,
  ORC_BC_addssl,
  ORC_BC_addusl,
  ORC_BC_andl,
  ORC_BC_andnl,
  ORC_BC_avgsl,
  ORC_BC_avgul,
  /* 110 */
  ORC_BC_cmpeql,
  ORC_BC_cmpgtsl,
  ORC_BC_copyl,
  ORC_BC_loadl,
  ORC_BC_loadoffl,
  ORC_BC_loadpl,
  ORC_BC_maxsl,
  ORC_BC_maxul,
  ORC_BC_minsl,
  ORC_BC_minul,
  /* 120 */
  ORC_BC_mulll,
  ORC_BC_mulhsl,
  ORC_BC_mulhul,
  ORC_BC_orl,
  ORC_BC_shll,
  ORC_BC_shrsl,
  ORC_BC_shrul,
  ORC_BC_signl,
  ORC_BC_storel,
  ORC_BC_subl,
  /* 130 */
  ORC_BC_subssl,
  ORC_BC_subusl,
  ORC_BC_xorl,
  ORC_BC_loadq,
  ORC_BC_loadpq,
  ORC_BC_storeq,
  ORC_BC_splatw3q,
  ORC_BC_copyq,
  ORC_BC_cmpeqq,
  ORC_BC_cmpgtsq,
  /* 140 */
  ORC_BC_andq,
  ORC_BC_andnq,
  ORC_BC_orq,
  ORC_BC_xorq,
  ORC_BC_addq,
  ORC_BC_subq,
  ORC_BC_shlq,
  ORC_BC_shrsq,
  ORC_BC_shruq,
  ORC_BC_convsbw,
  /* 150 */
  ORC_BC_convubw,
  ORC_BC_splatbw,
  ORC_BC_splatbl,
  ORC_BC_convswl,
  ORC_BC_convuwl,
  ORC_BC_convslq,
  ORC_BC_convulq,
  ORC_BC_convwb,
  ORC_BC_convhwb,
  ORC_BC_convssswb,
  /* 160 */
  ORC_BC_convsuswb,
  ORC_BC_convusswb,
  ORC_BC_convuuswb,
  ORC_BC_convlw,
  ORC_BC_convhlw,
  ORC_BC_convssslw,
  ORC_BC_convsuslw,
  ORC_BC_convusslw,
  ORC_BC_convuuslw,
  ORC_BC_convql,
  /* 170 */
  ORC_BC_convsssql,
  ORC_BC_convsusql,
  ORC_BC_convussql,
  ORC_BC_convuusql,
  ORC_BC_mulsbw,
  ORC_BC_mulubw,
  ORC_BC_mulswl,
  ORC_BC_muluwl,
  ORC_BC_mulslq,
  ORC_BC_mululq,
  /* 180 */
  ORC_BC_accw,
  ORC_BC_accl,
  ORC_BC_accsadubl,
  ORC_BC_swapw,
  ORC_BC_swapl,
  ORC_BC_swapwl,
  ORC_BC_swapq,
  ORC_BC_swaplq,
  ORC_BC_select0wb,
  ORC_BC_select1wb,
  /* 190 */
  ORC_BC_select0lw,
  ORC_BC_select1lw,
  ORC_BC_select0ql,
  ORC_BC_select1ql,
  ORC_BC_mergelq,
  ORC_BC_mergewl,
  ORC_BC_mergebw,
  ORC_BC_splitql,
  ORC_BC_splitlw,
  ORC_BC_splitwb,
  /* 200 */
  ORC_BC_addf,
  ORC_BC_subf,
  ORC_BC_mulf,
  ORC_BC_divf,
  ORC_BC_sqrtf,
  ORC_BC_maxf,
  ORC_BC_minf,
  ORC_BC_cmpeqf,
  ORC_BC_cmpltf,
  ORC_BC_cmplef,
  /* 210 */
  ORC_BC_convfl,
  ORC_BC_convlf,
  ORC_BC_addd,
  ORC_BC_subd,
  ORC_BC_muld,
  ORC_BC_divd,
  ORC_BC_sqrtd,
  ORC_BC_maxd,
  ORC_BC_mind,
  ORC_BC_cmpeqd,
  /* 220 */
  ORC_BC_cmpltd,
  ORC_BC_cmpled,
  ORC_BC_convdl,
  ORC_BC_convld,
  ORC_BC_convfd,
  ORC_BC_convdf,
  /* 226 */
  ORC_BC_LAST
} OrcBytecodes;
