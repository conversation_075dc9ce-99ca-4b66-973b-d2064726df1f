const express = require('express')
const { body, validationResult } = require('express-validator')
const WebsiteAnalyzer = require('../services/WebsiteAnalyzer')

const router = express.Router()

// Validation middleware
const validateURL = [
  body('url').isURL().withMessage('Valid URL is required')
]

// Analyze website endpoint
router.post('/website', validateURL, async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: errors.array() 
      })
    }

    const { url } = req.body
    const analyzer = new WebsiteAnalyzer()
    
    const analysisResult = await analyzer.analyze(url)
    
    res.json({
      success: true,
      data: analysisResult
    })

  } catch (error) {
    console.error('Website analysis error:', error)
    res.status(500).json({ 
      error: 'Website analysis failed', 
      message: error.message 
    })
  }
})

module.exports = router
