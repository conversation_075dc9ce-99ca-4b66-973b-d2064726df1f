const express = require('express')
const path = require('path')
const fs = require('fs')

const router = express.Router()

// Download generated app files
router.get('/:platform/:generationId', async (req, res) => {
  try {
    const { platform, generationId } = req.params
    
    // Validate platform
    if (!['android', 'ios'].includes(platform)) {
      return res.status(400).json({ error: 'Invalid platform' })
    }

    // Construct file path
    const fileExtension = platform === 'android' ? 'apk' : 'ipa'
    const fileName = `${generationId}.${fileExtension}`
    const filePath = path.join(__dirname, '../../generated-apps', fileName)

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'File not found' })
    }

    // Get file stats
    const stats = fs.statSync(filePath)
    
    // Set appropriate headers
    res.setHeader('Content-Type', 'application/octet-stream')
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`)
    res.setHeader('Content-Length', stats.size)

    // Stream the file
    const fileStream = fs.createReadStream(filePath)
    fileStream.pipe(res)

    fileStream.on('error', (error) => {
      console.error('File stream error:', error)
      res.status(500).json({ error: 'Download failed' })
    })

  } catch (error) {
    console.error('Download error:', error)
    res.status(500).json({ 
      error: 'Download failed', 
      message: error.message 
    })
  }
})

// Get download info
router.get('/info/:generationId', async (req, res) => {
  try {
    const { generationId } = req.params
    
    const androidPath = path.join(__dirname, '../../generated-apps', `${generationId}.apk`)
    const iosPath = path.join(__dirname, '../../generated-apps', `${generationId}.ipa`)

    const downloadInfo = {
      android: {
        available: fs.existsSync(androidPath),
        size: fs.existsSync(androidPath) ? fs.statSync(androidPath).size : 0,
        downloadUrl: `/api/download/android/${generationId}`
      },
      ios: {
        available: fs.existsSync(iosPath),
        size: fs.existsSync(iosPath) ? fs.statSync(iosPath).size : 0,
        downloadUrl: `/api/download/ios/${generationId}`
      }
    }

    res.json(downloadInfo)

  } catch (error) {
    console.error('Download info error:', error)
    res.status(500).json({ 
      error: 'Failed to get download info', 
      message: error.message 
    })
  }
})

module.exports = router
