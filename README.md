# URL to App PWA 📱

A Progressive Web App that converts any website URL into downloadable mobile applications for Android and iOS platforms.

## 🚀 Features

- **No Registration Required**: Start converting URLs immediately
- **Fast Generation**: Apps generated in under 5 minutes
- **Dual Platform Support**: Creates both Android APK and iOS IPA files
- **Design Customization**: Multiple themes, layouts, and color schemes
- **Live Preview**: See how your app will look before generation
- **Offline Functionality**: PWA works offline with cached content
- **Responsive Design**: Works perfectly on desktop and mobile
- **Real-time Progress**: Track generation progress with detailed steps

## 🛠️ Technology Stack

### Frontend
- **React 18** - Modern UI framework
- **Vite** - Fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **PWA Features** - Service worker, offline support, installable

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **Puppeteer** - Web scraping and analysis
- **Sharp** - Image processing
- **Archiver** - File compression for app packages

### Key Libraries
- **Cheerio** - Server-side HTML parsing
- **Axios** - HTTP client
- **Socket.io** - Real-time communication
- **Helmet** - Security middleware
- **Express Rate Limit** - API rate limiting

## 📋 Prerequisites

- Node.js 18.0.0 or higher
- npm or yarn package manager
- 4GB+ RAM (for Puppeteer browser instances)
- 10GB+ free disk space (for generated apps)

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/your-username/url-to-app-pwa.git
cd url-to-app-pwa
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Setup
```bash
cp .env.example .env
```

Edit `.env` file with your configuration:
```env
PORT=5000
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000
MAX_CONCURRENT_GENERATIONS=5
GENERATION_TIMEOUT_MS=300000
```

### 4. Start Development Server
```bash
npm run dev
```

This starts both frontend (port 3000) and backend (port 5000) servers.

### 5. Access the Application
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000/api

## 🏗️ Project Structure

```
url-to-app-pwa/
├── public/                 # Static files
│   ├── manifest.json      # PWA manifest
│   ├── sw.js             # Service worker
│   └── offline.html      # Offline page
├── src/                   # Frontend source
│   ├── components/       # React components
│   ├── App.jsx          # Main app component
│   ├── main.jsx         # Entry point
│   └── index.css        # Global styles
├── server/               # Backend source
│   ├── routes/          # API routes
│   ├── services/        # Business logic
│   └── index.js         # Server entry point
├── generated-apps/       # Output directory
├── uploads/             # Temporary uploads
└── temp/               # Temporary files
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 5000 |
| `NODE_ENV` | Environment | development |
| `CORS_ORIGIN` | Frontend URL | http://localhost:3000 |
| `RATE_LIMIT_WINDOW_MS` | Rate limit window | 900000 (15 min) |
| `RATE_LIMIT_MAX_REQUESTS` | Max requests per window | 100 |
| `MAX_CONCURRENT_GENERATIONS` | Concurrent app generations | 5 |
| `GENERATION_TIMEOUT_MS` | Generation timeout | 300000 (5 min) |

### Customization Options

#### Themes
- Modern (default)
- Classic
- Minimal
- Dark

#### Layouts
- Standard - Traditional header/content layout
- Tabbed - Bottom navigation tabs
- Drawer - Side navigation drawer

#### Colors
- Primary color (hex)
- Secondary color (hex)
- Custom color picker support

## 📱 How It Works

### 1. URL Analysis
- Extracts website metadata (title, description, favicon)
- Captures screenshot for preview
- Analyzes content structure and navigation
- Identifies key images and media

### 2. App Generation Process
1. **Asset Preparation** (20%) - Process icons, splash screens
2. **Android Build** (40%) - Create WebView-based Android app
3. **iOS Build** (70%) - Create WebView-based iOS app
4. **Finalization** (100%) - Package and prepare downloads

### 3. Generated App Features
- WebView container for website content
- Offline caching capabilities
- Native app appearance
- Platform-specific optimizations
- Custom branding and theming

## 🔌 API Documentation

### Analyze Website
```http
POST /api/analyze/website
Content-Type: application/json

{
  "url": "https://example.com"
}
```

### Generate App
```http
POST /api/generate/app
Content-Type: application/json

{
  "url": "https://example.com",
  "title": "My App",
  "description": "App description",
  "theme": "modern",
  "layout": "standard",
  "colors": {
    "primary": "#3b82f6",
    "secondary": "#64748b"
  }
}
```

### Download App
```http
GET /api/download/{platform}/{generationId}
```

Where `platform` is either `android` or `ios`.

## 🚀 Deployment

### Production Build
```bash
npm run build
npm start
```

### Docker Deployment
```bash
# Build image
docker build -t url-to-app-pwa .

# Run container
docker run -p 5000:5000 -e NODE_ENV=production url-to-app-pwa
```

### Environment Setup for Production
- Set `NODE_ENV=production`
- Configure proper CORS origins
- Set up SSL/TLS certificates
- Configure rate limiting
- Set up monitoring and logging

## 🔒 Security Considerations

- Rate limiting on API endpoints
- Input validation and sanitization
- CORS configuration
- Helmet.js security headers
- File upload restrictions
- Generated app sandboxing

## 🧪 Testing

### Run Tests
```bash
npm test
```

### Test Coverage
```bash
npm run test:coverage
```

### Manual Testing Checklist
- [ ] URL validation and analysis
- [ ] Design customization
- [ ] App preview functionality
- [ ] Generation progress tracking
- [ ] File download functionality
- [ ] Offline PWA features
- [ ] Mobile responsiveness

## 🐛 Troubleshooting

### Common Issues

**Puppeteer Installation Issues**
```bash
# Install Chromium dependencies (Ubuntu/Debian)
sudo apt-get install -y gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 ca-certificates fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils wget
```

**Memory Issues**
- Increase server memory allocation
- Reduce `MAX_CONCURRENT_GENERATIONS`
- Monitor disk space usage

**Generation Failures**
- Check website accessibility
- Verify URL format
- Review server logs
- Check timeout settings

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- React team for the amazing framework
- Puppeteer team for web automation tools
- Tailwind CSS for the utility-first approach
- All open-source contributors

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/url-to-app-pwa/issues)
- 📖 Documentation: [Wiki](https://github.com/your-username/url-to-app-pwa/wiki)

---

Made with ❤️ for the developer community
