{"name": "url-to-app-pwa", "version": "1.1.0", "description": "Progressive Web App that converts URLs into downloadable mobile applications", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "build": "npm run client:build", "start": "node server/index.js", "server:dev": "nodemon server/index.js", "client:dev": "vite", "client:build": "vite build", "client:preview": "vite preview", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "format": "prettier --write ."}, "keywords": ["pwa", "mobile-app", "url-converter", "android", "ios", "app-generator"], "author": "URL to App PWA", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "multer": "^1.4.5-lts.1", "puppeteer": "^21.5.2", "cheerio": "^1.0.0-rc.12", "axios": "^1.6.2", "sharp": "^0.32.6", "archiver": "^6.0.1", "uuid": "^9.0.1", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "socket.io": "^4.7.4"}, "devDependencies": {"vite": "^5.0.0", "vite-plugin-pwa": "^0.17.4", "@vitejs/plugin-react": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "concurrently": "^8.2.2", "nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.55.0", "prettier": "^3.1.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}