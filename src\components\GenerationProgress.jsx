import React, { useEffect, useState } from 'react'

const GenerationProgress = ({ progress, isGenerating, onSkip }) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [timeElapsed, setTimeElapsed] = useState(0)

  const steps = [
    { name: 'Analyzing Website', description: 'Extracting content and structure', minProgress: 0, maxProgress: 20 },
    { name: 'Processing Assets', description: 'Optimizing images and resources', minProgress: 20, maxProgress: 40 },
    { name: 'Building Android App', description: 'Creating APK package', minProgress: 40, maxProgress: 65 },
    { name: 'Building iOS App', description: 'Creating IPA package', minProgress: 65, maxProgress: 90 },
    { name: 'Finalizing', description: 'Preparing downloads', minProgress: 90, maxProgress: 100 }
  ]

  useEffect(() => {
    // Update current step based on progress
    let newStep = 0
    for (let i = 0; i < steps.length; i++) {
      if (progress >= steps[i].minProgress && progress < steps[i].maxProgress) {
        newStep = i
        break
      } else if (progress >= steps[i].maxProgress) {
        newStep = i + 1
      }
    }
    // Ensure we don't exceed the last step
    newStep = Math.min(newStep, steps.length - 1)

    // Debug logging
    console.log(`Progress: ${progress}%, Current Step: ${newStep}, Step Name: ${steps[newStep]?.name}`)

    setCurrentStep(newStep)
  }, [progress])

  useEffect(() => {
    let interval
    if (isGenerating) {
      interval = setInterval(() => {
        setTimeElapsed(prev => prev + 1)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isGenerating])

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Better estimated time calculation
  const estimatedTimeRemaining = (() => {
    if (progress >= 100) return 0
    if (progress <= 5 || timeElapsed <= 0) return 120 // Default 2 minutes

    const rate = progress / timeElapsed
    const remaining = (100 - progress) / rate
    return Math.max(0, Math.ceil(remaining))
  })()

  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Generating Your Apps
        </h2>
        <p className="text-lg text-gray-600">
          Please wait while we create your mobile applications
        </p>
      </div>

      <div className="card">
        {/* Overall Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Overall Progress</span>
            <span className="text-sm font-medium text-gray-700">{Math.round(progress)}%</span>
          </div>
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Time Information */}
        <div className="grid grid-cols-2 gap-4 mb-8">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{formatTime(timeElapsed)}</div>
            <div className="text-sm text-gray-500">Time Elapsed</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{formatTime(estimatedTimeRemaining)}</div>
            <div className="text-sm text-gray-500">Est. Remaining</div>
          </div>
        </div>

        {/* Step Progress */}
        <div className="space-y-4">
          {steps.map((step, index) => (
            <div key={index} className="flex items-center">
              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                index < currentStep 
                  ? 'bg-green-500 text-white' 
                  : index === currentStep 
                    ? 'bg-primary-500 text-white' 
                    : 'bg-gray-200 text-gray-500'
              }`}>
                {index < currentStep ? (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : index === currentStep ? (
                  <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                ) : (
                  index + 1
                )}
              </div>
              <div className="ml-4 flex-1">
                <div className={`font-medium ${
                  index <= currentStep ? 'text-gray-900' : 'text-gray-500'
                }`}>
                  {step.name}
                </div>
                <div className={`text-sm ${
                  index <= currentStep ? 'text-gray-600' : 'text-gray-400'
                }`}>
                  {step.description}
                </div>
              </div>
              {index === currentStep && (
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-primary-500 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Status Messages */}
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex">
            <svg className="w-5 h-5 text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Generation in Progress
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  We're creating optimized mobile apps from your website. This process typically takes 3-5 minutes.
                  Please keep this tab open until completion.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Fun Facts */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Did you know?</h4>
          <p className="text-sm text-gray-600">
            Your generated apps will include offline functionality, allowing users to access content even without an internet connection.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="mt-6 text-center space-y-2">
          {/* Development: Force Complete Button */}
          {progress < 100 && onSkip && (
            <div>
              <button
                onClick={onSkip}
                className="px-4 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors mr-4"
              >
                Force Complete (Demo)
              </button>
            </div>
          )}

          {/* Skip Button - show after 8 seconds */}
          {timeElapsed > 8 && progress < 100 && onSkip && (
            <div>
              <button
                onClick={onSkip}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 underline transition-colors"
              >
                Taking too long? Skip to download demo files
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default GenerationProgress
