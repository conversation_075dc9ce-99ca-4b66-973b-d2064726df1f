import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import App from './App'

// Mock the components to avoid complex dependencies in unit tests
jest.mock('./components/Header', () => {
  return function MockHeader() {
    return <div data-testid="header">Header</div>
  }
})

jest.mock('./components/Footer', () => {
  return function MockFooter() {
    return <div data-testid="footer">Footer</div>
  }
})

describe('App Component', () => {
  test('renders without crashing', () => {
    render(<App />)
    expect(screen.getByTestId('header')).toBeInTheDocument()
    expect(screen.getByTestId('footer')).toBeInTheDocument()
  })

  test('shows URL input step initially', () => {
    render(<App />)
    expect(screen.getByText('Turn Any Website Into a Mobile App')).toBeInTheDocument()
  })

  test('displays step indicator', () => {
    render(<App />)
    // Check if step indicator is present
    expect(screen.getByText('URL Input')).toBeInTheDocument()
    expect(screen.getByText('Customize')).toBeInTheDocument()
    expect(screen.getByText('Preview')).toBeInTheDocument()
    expect(screen.getByText('Generate')).toBeInTheDocument()
    expect(screen.getByText('Download')).toBeInTheDocument()
  })

  test('progresses through steps correctly', async () => {
    render(<App />)
    
    // Start at step 1
    expect(screen.getByText('Enter website URL')).toBeInTheDocument()
    
    // Mock URL input and submission
    const urlInput = screen.getByPlaceholderText('https://example.com')
    const analyzeButton = screen.getByText('Analyze Website')
    
    fireEvent.change(urlInput, { target: { value: 'https://example.com' } })
    fireEvent.click(analyzeButton)
    
    // Should progress to step 2 (this would require proper mocking of the API)
    // For now, we just test that the component doesn't crash
    expect(urlInput).toBeInTheDocument()
  })

  test('handles reset functionality', () => {
    render(<App />)
    
    // The reset functionality would be tested when we reach the download step
    // For now, we just ensure the component renders properly
    expect(screen.getByTestId('header')).toBeInTheDocument()
  })
})

describe('App Integration', () => {
  test('maintains app state correctly', () => {
    render(<App />)
    
    // Test that initial state is set correctly
    expect(screen.getByText('URL Input')).toBeInTheDocument()
    
    // Test that the app doesn't crash with various interactions
    const urlInput = screen.getByPlaceholderText('https://example.com')
    fireEvent.change(urlInput, { target: { value: 'invalid-url' } })
    fireEvent.change(urlInput, { target: { value: 'https://valid-url.com' } })
    
    expect(urlInput.value).toBe('https://valid-url.com')
  })

  test('handles error states gracefully', () => {
    render(<App />)
    
    // Test error handling by trying to submit invalid URL
    const urlInput = screen.getByPlaceholderText('https://example.com')
    const analyzeButton = screen.getByText('Analyze Website')
    
    fireEvent.change(urlInput, { target: { value: 'not-a-url' } })
    fireEvent.click(analyzeButton)
    
    // Should show error message
    expect(screen.getByText('Please enter a valid URL')).toBeInTheDocument()
  })
})
