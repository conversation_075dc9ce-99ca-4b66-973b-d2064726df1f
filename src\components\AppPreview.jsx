import React, { useState, useEffect } from 'react'

const AppPreview = ({ appData, onConfirm, onBack }) => {
  const [previewData, setPreviewData] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    // Simulate loading and prepare preview data
    const preparePreview = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Prepare preview data with website information
        const preview = {
          ...appData,
          websiteInfo: appData.websiteData || {},
          mockNavigation: [
            { name: 'Home', icon: '🏠', active: true },
            { name: 'About', icon: 'ℹ️', active: false },
            { name: 'Services', icon: '⚙️', active: false },
            { name: 'Contact', icon: '📞', active: false }
          ]
        }

        setPreviewData(preview)
      } catch (err) {
        setError('Failed to load preview')
      } finally {
        setIsLoading(false)
      }
    }

    preparePreview()
  }, [appData])

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Preparing Preview
          </h2>
          <p className="text-lg text-gray-600">
            Generating app preview...
          </p>
        </div>
        <div className="card">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="card">
          <div className="text-center py-12">
            <div className="text-red-500 mb-4">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{error}</h3>
            <button onClick={() => window.location.reload()} className="btn-primary">
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Preview Your App
        </h2>
        <p className="text-lg text-gray-600">
          Review how your app will look and function before generating
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Mobile App Preview */}
        <div className="lg:col-span-2">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Mobile App Preview</h3>

            <div className="flex justify-center">
              <div className="relative">
                {/* Phone Frame */}
                <div className="w-80 h-[600px] bg-black rounded-[3rem] p-3 shadow-2xl">
                  <div className="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
                    {/* Status Bar */}
                    <div className="h-8 bg-black flex items-center justify-between px-6 text-white text-sm font-medium">
                      <span>9:41</span>
                      <div className="flex items-center space-x-1">
                        <div className="flex space-x-1">
                          <div className="w-1 h-1 bg-white rounded-full"></div>
                          <div className="w-1 h-1 bg-white rounded-full"></div>
                          <div className="w-1 h-1 bg-white rounded-full"></div>
                        </div>
                        <div className="w-6 h-3 border border-white rounded-sm">
                          <div className="w-4 h-1 bg-white rounded-sm mt-0.5 ml-0.5"></div>
                        </div>
                      </div>
                    </div>

                    {/* App Header */}
                    <div
                      className="h-16 flex items-center justify-between px-4 text-white shadow-sm"
                      style={{ backgroundColor: previewData.colors.primary }}
                    >
                      {previewData.layout === 'drawer' && (
                        <button className="p-2">
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                          </svg>
                        </button>
                      )}
                      <h1 className="text-lg font-semibold flex-1 text-center">
                        {previewData.title}
                      </h1>
                      <button className="p-2">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                        </svg>
                      </button>
                    </div>

                    {/* App Content */}
                    <div className="flex-1 overflow-hidden">
                      <div className="h-full overflow-y-auto p-4 pb-20">
                        {/* Website Screenshot or Placeholder */}
                        {previewData.websiteInfo?.screenshot ? (
                          <div className="mb-4">
                            <img
                              src={`data:${previewData.websiteInfo.screenshot.mimeType};base64,${previewData.websiteInfo.screenshot.data}`}
                              alt="Website preview"
                              className="w-full h-48 object-cover rounded-lg shadow-sm"
                              onError={(e) => {
                                e.target.style.display = 'none'
                                e.target.nextSibling.style.display = 'flex'
                              }}
                            />
                            <div className="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg shadow-sm hidden items-center justify-center">
                              <div className="text-center">
                                <div className="w-16 h-16 bg-gray-300 rounded-full mx-auto mb-2 flex items-center justify-center">
                                  <svg className="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                  </svg>
                                </div>
                                <p className="text-gray-500 text-sm">Website Preview</p>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg shadow-sm mb-4 flex items-center justify-center">
                            <div className="text-center">
                              <div className="w-16 h-16 bg-gray-300 rounded-full mx-auto mb-2 flex items-center justify-center">
                                <svg className="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                                </svg>
                              </div>
                              <p className="text-gray-500 text-sm">Website Content</p>
                            </div>
                          </div>
                        )}

                        {/* App Description */}
                        <div className="mb-6">
                          <h2 className="text-xl font-bold text-gray-900 mb-2">{previewData.title}</h2>
                          <p className="text-gray-600 text-sm leading-relaxed">{previewData.description}</p>
                        </div>

                        {/* Navigation Items */}
                        <div className="space-y-3">
                          <h3 className="text-lg font-semibold text-gray-900">Quick Access</h3>
                          {previewData.mockNavigation.map((item, index) => (
                            <div
                              key={index}
                              className={`flex items-center p-3 rounded-lg transition-colors ${
                                item.active
                                  ? 'bg-blue-50 border border-blue-200'
                                  : 'bg-gray-50 hover:bg-gray-100'
                              }`}
                            >
                              <span className="text-xl mr-3">{item.icon}</span>
                              <span className={`font-medium ${
                                item.active ? 'text-blue-700' : 'text-gray-700'
                              }`}>
                                {item.name}
                              </span>
                              {item.active && (
                                <div className="ml-auto w-2 h-2 bg-blue-500 rounded-full"></div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Bottom Navigation (if tabbed layout) */}
                    {previewData.layout === 'tabbed' && (
                      <div
                        className="absolute bottom-0 left-0 right-0 h-16 flex items-center justify-around border-t bg-white"
                        style={{ borderColor: previewData.colors.secondary + '40' }}
                      >
                        {previewData.mockNavigation.slice(0, 4).map((item, index) => (
                          <div key={index} className="flex flex-col items-center py-2">
                            <div className={`text-lg mb-1 ${
                              item.active ? 'opacity-100' : 'opacity-60'
                            }`}>
                              {item.icon}
                            </div>
                            <span className={`text-xs ${
                              item.active
                                ? 'text-blue-600 font-medium'
                                : 'text-gray-500'
                            }`}>
                              {item.name}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* App Details */}
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">App Details</h3>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">App Name</label>
                <p className="text-gray-900">{appData.title}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Description</label>
                <p className="text-gray-900">{appData.description}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Source URL</label>
                <p className="text-gray-900 break-all">{appData.url}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Theme</label>
                <p className="text-gray-900 capitalize">{appData.theme}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Layout</label>
                <p className="text-gray-900 capitalize">{appData.layout}</p>
              </div>
              <div className="flex space-x-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Primary Color</label>
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-6 h-6 rounded border"
                      style={{ backgroundColor: appData.colors.primary }}
                    ></div>
                    <span className="text-gray-900">{appData.colors.primary}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Secondary Color</label>
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-6 h-6 rounded border"
                      style={{ backgroundColor: appData.colors.secondary }}
                    ></div>
                    <span className="text-gray-900">{appData.colors.secondary}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">What You'll Get</h3>
            <div className="space-y-3">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-900">Android APK file ready for installation</span>
              </div>
              <div className="flex items-center">
                <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-900">iOS IPA file for testing and distribution</span>
              </div>
              <div className="flex items-center">
                <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-900">Optimized for mobile performance</span>
              </div>
              <div className="flex items-center">
                <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-900">Offline functionality included</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between mt-8">
        <button
          onClick={onBack}
          className="btn-secondary px-6 py-3"
        >
          ← Back to Customize
        </button>
        <button
          onClick={onConfirm}
          className="btn-primary px-8 py-3 text-lg font-semibold"
        >
          Generate Apps →
        </button>
      </div>
    </div>
  )
}

export default AppPreview
