# Deployment Guide 🚀

This guide covers various deployment options for the URL to App PWA.

## 📋 Prerequisites

- Node.js 18+ installed
- Docker (for containerized deployment)
- SSL certificate (for production)
- Domain name (recommended)

## 🖥️ Local Development

### 1. <PERSON>lone and Setup
```bash
git clone https://github.com/your-username/url-to-app-pwa.git
cd url-to-app-pwa
npm install
cp .env.example .env
```

### 2. Configure Environment
Edit `.env` file:
```env
NODE_ENV=development
PORT=5000
CORS_ORIGIN=http://localhost:3000
```

### 3. Start Development Server
```bash
npm run dev
```

## 🐳 Docker Deployment

### Option 1: Docker Compose (Recommended)
```bash
# Clone repository
git clone https://github.com/your-username/url-to-app-pwa.git
cd url-to-app-pwa

# Start services
docker-compose up -d

# View logs
docker-compose logs -f app
```

### Option 2: Docker Build
```bash
# Build image
docker build -t url-to-app-pwa .

# Run container
docker run -d \
  --name url-to-app \
  -p 5000:5000 \
  -e NODE_ENV=production \
  -v $(pwd)/generated-apps:/app/generated-apps \
  url-to-app-pwa
```

## ☁️ Cloud Deployment

### Heroku

1. **Install Heroku CLI**
```bash
npm install -g heroku
```

2. **Create Heroku App**
```bash
heroku create your-app-name
```

3. **Configure Environment**
```bash
heroku config:set NODE_ENV=production
heroku config:set NPM_CONFIG_PRODUCTION=false
heroku config:set MAX_CONCURRENT_GENERATIONS=2
```

4. **Add Buildpacks**
```bash
heroku buildpacks:add heroku/nodejs
heroku buildpacks:add jontewks/puppeteer
```

5. **Deploy**
```bash
git push heroku main
```

### Vercel

1. **Install Vercel CLI**
```bash
npm install -g vercel
```

2. **Configure vercel.json**
```json
{
  "version": 2,
  "builds": [
    {
      "src": "server/index.js",
      "use": "@vercel/node"
    },
    {
      "src": "package.json",
      "use": "@vercel/static-build"
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/server/index.js"
    },
    {
      "src": "/(.*)",
      "dest": "/dist/$1"
    }
  ]
}
```

3. **Deploy**
```bash
vercel --prod
```

### DigitalOcean App Platform

1. **Create App**
- Connect GitHub repository
- Choose Node.js environment
- Set build command: `npm run build`
- Set run command: `npm start`

2. **Environment Variables**
```
NODE_ENV=production
MAX_CONCURRENT_GENERATIONS=2
GENERATION_TIMEOUT_MS=300000
```

### AWS EC2

1. **Launch EC2 Instance**
- Ubuntu 20.04 LTS
- t3.medium or larger
- Security group: HTTP (80), HTTPS (443), SSH (22)

2. **Install Dependencies**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2
sudo npm install -g pm2

# Install Nginx
sudo apt install nginx -y
```

3. **Deploy Application**
```bash
# Clone repository
git clone https://github.com/your-username/url-to-app-pwa.git
cd url-to-app-pwa

# Install dependencies
npm install

# Build application
npm run build

# Start with PM2
pm2 start server/index.js --name "url-to-app"
pm2 startup
pm2 save
```

4. **Configure Nginx**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🔒 SSL/HTTPS Setup

### Let's Encrypt (Free)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Cloudflare (Recommended)
1. Add domain to Cloudflare
2. Update nameservers
3. Enable SSL/TLS encryption
4. Configure origin certificates

## 📊 Monitoring & Logging

### PM2 Monitoring
```bash
# Monitor processes
pm2 monit

# View logs
pm2 logs url-to-app

# Restart application
pm2 restart url-to-app
```

### Log Management
```bash
# Install log rotation
sudo apt install logrotate

# Configure log rotation
sudo nano /etc/logrotate.d/url-to-app
```

### Health Checks
```bash
# Add to crontab
*/5 * * * * curl -f http://localhost:5000/api/health || systemctl restart url-to-app
```

## 🔧 Performance Optimization

### 1. Enable Compression
```javascript
// Already included in server/index.js
app.use(compression())
```

### 2. Configure Caching
```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. Optimize Puppeteer
```javascript
// In production, use these Puppeteer args
const browser = await puppeteer.launch({
  headless: 'new',
  args: [
    '--no-sandbox',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',
    '--disable-accelerated-2d-canvas',
    '--no-first-run',
    '--no-zygote',
    '--single-process',
    '--disable-gpu'
  ]
})
```

## 🛡️ Security Hardening

### 1. Environment Variables
```bash
# Never commit these to version control
NODE_ENV=production
JWT_SECRET=your-super-secret-key
ENCRYPTION_KEY=your-32-character-key
```

### 2. Firewall Configuration
```bash
# UFW (Ubuntu)
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https
sudo ufw enable
```

### 3. Rate Limiting
```javascript
// Adjust based on your needs
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
})
```

## 📈 Scaling Considerations

### Horizontal Scaling
- Use load balancer (Nginx, HAProxy)
- Deploy multiple instances
- Implement session storage (Redis)
- Use CDN for static assets

### Vertical Scaling
- Increase server resources
- Optimize memory usage
- Use SSD storage
- Monitor resource usage

### Database Scaling
- Implement Redis for caching
- Use database clustering
- Implement read replicas
- Consider MongoDB for metadata

## 🔄 CI/CD Pipeline

### GitHub Actions
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Deploy to server
      run: |
        # Your deployment script here
```

## 🆘 Troubleshooting

### Common Issues

1. **Puppeteer Installation**
```bash
# Install missing dependencies
sudo apt-get install -y gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 ca-certificates fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils wget
```

2. **Memory Issues**
```bash
# Increase swap space
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

3. **Port Issues**
```bash
# Check port usage
sudo netstat -tulpn | grep :5000

# Kill process using port
sudo kill -9 $(sudo lsof -t -i:5000)
```

## 📞 Support

For deployment issues:
- 📧 Email: <EMAIL>
- 🐛 GitHub Issues: [Report Issue](https://github.com/your-username/url-to-app-pwa/issues)
- 📖 Documentation: [Wiki](https://github.com/your-username/url-to-app-pwa/wiki)
