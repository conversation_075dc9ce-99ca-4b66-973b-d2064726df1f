# Server Configuration
PORT=5000
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=50MB
UPLOAD_DIR=./uploads
TEMP_DIR=./temp
OUTPUT_DIR=./output

# App Generation Configuration
MAX_CONCURRENT_GENERATIONS=5
GENERATION_TIMEOUT_MS=300000

# Security
JWT_SECRET=your-super-secret-jwt-key-here
ENCRYPTION_KEY=your-32-character-encryption-key

# External Services (Optional)
GOOGLE_PLAY_DEVELOPER_KEY=
APPLE_DEVELOPER_CERTIFICATE=

# Monitoring and Logging
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Database (if needed for future features)
DATABASE_URL=

# Redis (for caching and queue management)
REDIS_URL=redis://localhost:6379
