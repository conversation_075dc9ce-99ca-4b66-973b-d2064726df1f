const puppeteer = require('puppeteer')
const cheerio = require('cheerio')
const axios = require('axios')
const sharp = require('sharp')
const fs = require('fs')
const path = require('path')

class WebsiteAnalyzer {
  constructor() {
    this.browser = null
  }

  async analyze(url) {
    try {
      // Launch browser
      this.browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      })

      const page = await this.browser.newPage()
      await page.setViewport({ width: 1200, height: 800 })

      // Navigate to the URL
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 })

      // Extract basic information
      const basicInfo = await this.extractBasicInfo(page)
      
      // Extract content structure
      const contentStructure = await this.extractContentStructure(page)
      
      // Extract navigation
      const navigation = await this.extractNavigation(page)
      
      // Extract images and media
      const media = await this.extractMedia(page, url)
      
      // Take screenshot
      const screenshot = await this.takeScreenshot(page)
      
      // Extract favicon
      const favicon = await this.extractFavicon(page, url)

      await this.browser.close()

      return {
        url,
        basicInfo,
        contentStructure,
        navigation,
        media,
        screenshot,
        favicon,
        analyzedAt: new Date().toISOString()
      }

    } catch (error) {
      if (this.browser) {
        await this.browser.close()
      }
      throw new Error(`Website analysis failed: ${error.message}`)
    }
  }

  async extractBasicInfo(page) {
    return await page.evaluate(() => {
      const getMetaContent = (name) => {
        const meta = document.querySelector(`meta[name="${name}"], meta[property="${name}"]`)
        return meta ? meta.getAttribute('content') : null
      }

      return {
        title: document.title || '',
        description: getMetaContent('description') || getMetaContent('og:description') || '',
        keywords: getMetaContent('keywords') || '',
        author: getMetaContent('author') || '',
        language: document.documentElement.lang || 'en',
        charset: document.characterSet || 'UTF-8',
        viewport: getMetaContent('viewport') || '',
        themeColor: getMetaContent('theme-color') || '#000000'
      }
    })
  }

  async extractContentStructure(page) {
    return await page.evaluate(() => {
      const structure = {
        headings: [],
        paragraphs: 0,
        links: 0,
        images: 0,
        forms: 0,
        sections: []
      }

      // Extract headings
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
      headings.forEach(heading => {
        structure.headings.push({
          level: parseInt(heading.tagName.charAt(1)),
          text: heading.textContent.trim(),
          id: heading.id || ''
        })
      })

      // Count elements
      structure.paragraphs = document.querySelectorAll('p').length
      structure.links = document.querySelectorAll('a[href]').length
      structure.images = document.querySelectorAll('img').length
      structure.forms = document.querySelectorAll('form').length

      // Extract main sections
      const sections = document.querySelectorAll('section, article, main, aside, header, footer')
      sections.forEach(section => {
        structure.sections.push({
          tag: section.tagName.toLowerCase(),
          id: section.id || '',
          class: section.className || '',
          textLength: section.textContent.trim().length
        })
      })

      return structure
    })
  }

  async extractNavigation(page) {
    return await page.evaluate(() => {
      const navigation = {
        mainNav: [],
        breadcrumbs: [],
        footer: []
      }

      // Extract main navigation
      const navElements = document.querySelectorAll('nav a, .nav a, .navigation a, .menu a')
      navElements.forEach(link => {
        if (link.href && link.textContent.trim()) {
          navigation.mainNav.push({
            text: link.textContent.trim(),
            href: link.href,
            isExternal: !link.href.includes(window.location.hostname)
          })
        }
      })

      // Extract breadcrumbs
      const breadcrumbElements = document.querySelectorAll('.breadcrumb a, .breadcrumbs a, [aria-label="breadcrumb"] a')
      breadcrumbElements.forEach(link => {
        if (link.href && link.textContent.trim()) {
          navigation.breadcrumbs.push({
            text: link.textContent.trim(),
            href: link.href
          })
        }
      })

      // Extract footer links
      const footerElements = document.querySelectorAll('footer a')
      footerElements.forEach(link => {
        if (link.href && link.textContent.trim()) {
          navigation.footer.push({
            text: link.textContent.trim(),
            href: link.href,
            isExternal: !link.href.includes(window.location.hostname)
          })
        }
      })

      return navigation
    })
  }

  async extractMedia(page, baseUrl) {
    return await page.evaluate((baseUrl) => {
      const media = {
        images: [],
        videos: [],
        audio: []
      }

      // Extract images
      const images = document.querySelectorAll('img')
      images.forEach(img => {
        if (img.src) {
          media.images.push({
            src: img.src,
            alt: img.alt || '',
            width: img.naturalWidth || 0,
            height: img.naturalHeight || 0,
            isLogo: img.className.toLowerCase().includes('logo') || img.alt.toLowerCase().includes('logo')
          })
        }
      })

      // Extract videos
      const videos = document.querySelectorAll('video')
      videos.forEach(video => {
        if (video.src || video.querySelector('source')) {
          media.videos.push({
            src: video.src || video.querySelector('source')?.src || '',
            poster: video.poster || '',
            duration: video.duration || 0
          })
        }
      })

      // Extract audio
      const audios = document.querySelectorAll('audio')
      audios.forEach(audio => {
        if (audio.src || audio.querySelector('source')) {
          media.audio.push({
            src: audio.src || audio.querySelector('source')?.src || '',
            duration: audio.duration || 0
          })
        }
      })

      return media
    }, baseUrl)
  }

  async takeScreenshot(page) {
    try {
      const screenshotBuffer = await page.screenshot({
        fullPage: true,
        type: 'png'
      })

      // Resize screenshot for preview
      const resizedBuffer = await sharp(screenshotBuffer)
        .resize(400, 600, { fit: 'inside', withoutEnlargement: true })
        .png()
        .toBuffer()

      return {
        data: resizedBuffer.toString('base64'),
        mimeType: 'image/png'
      }
    } catch (error) {
      console.error('Screenshot error:', error)
      return null
    }
  }

  async extractFavicon(page, baseUrl) {
    try {
      const faviconUrl = await page.evaluate(() => {
        const favicon = document.querySelector('link[rel="icon"], link[rel="shortcut icon"], link[rel="apple-touch-icon"]')
        return favicon ? favicon.href : '/favicon.ico'
      })

      // Download favicon
      const response = await axios.get(faviconUrl, { responseType: 'arraybuffer' })
      const buffer = Buffer.from(response.data)

      // Convert to PNG and resize
      const processedBuffer = await sharp(buffer)
        .resize(192, 192, { fit: 'inside', withoutEnlargement: true })
        .png()
        .toBuffer()

      return {
        data: processedBuffer.toString('base64'),
        mimeType: 'image/png'
      }
    } catch (error) {
      console.error('Favicon extraction error:', error)
      return null
    }
  }
}

module.exports = WebsiteAnalyzer
