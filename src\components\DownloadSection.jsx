import React, { useState, useEffect } from 'react'

const DownloadSection = ({ downloadLinks, appData, onReset }) => {
  const [downloadInfo, setDownloadInfo] = useState(null)
  const [downloading, setDownloading] = useState({ android: false, ios: false })
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (downloadLinks?.generationId) {
      fetchDownloadInfo(downloadLinks.generationId)
    } else {
      // Simulate file generation completion
      simulateFileGeneration()
    }
  }, [downloadLinks])

  const simulateFileGeneration = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Simulate file generation delay
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Create mock download info
      const mockInfo = {
        android: {
          available: true,
          size: Math.floor(Math.random() * 10000000) + 5000000, // 5-15MB
          downloadUrl: `/api/download/android/mock-${Date.now()}`
        },
        ios: {
          available: true,
          size: Math.floor(Math.random() * 15000000) + 8000000, // 8-23MB
          downloadUrl: `/api/download/ios/mock-${Date.now()}`
        }
      }

      setDownloadInfo(mockInfo)
    } catch (err) {
      setError('Failed to prepare downloads')
    } finally {
      setIsLoading(false)
    }
  }

  const fetchDownloadInfo = async (generationId) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/download/info/${generationId}`)
      if (response.ok) {
        const info = await response.json()
        setDownloadInfo(info)
      } else {
        throw new Error('Failed to fetch download info')
      }
    } catch (error) {
      console.error('Failed to fetch download info:', error)
      // Fallback to mock data
      await simulateFileGeneration()
    } finally {
      setIsLoading(false)
    }
  }

  const handleDownload = async (platform) => {
    setDownloading(prev => ({ ...prev, [platform]: true }))

    try {
      // Create a mock file for demonstration
      const mockFileContent = createMockAppFile(platform)
      const blob = new Blob([mockFileContent], {
        type: platform === 'android' ? 'application/vnd.android.package-archive' : 'application/octet-stream'
      })

      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${appData.title.replace(/[^a-zA-Z0-9]/g, '_')}.${platform === 'android' ? 'apk' : 'ipa'}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      // Show success message
      showDownloadSuccess(platform)

    } catch (error) {
      console.error('Download error:', error)
      alert(`Download failed for ${platform}. Please try again.`)
    } finally {
      setDownloading(prev => ({ ...prev, [platform]: false }))
    }
  }

  const createMockAppFile = (platform) => {
    const appInfo = {
      name: appData.title,
      description: appData.description,
      url: appData.url,
      theme: appData.theme,
      layout: appData.layout,
      colors: appData.colors,
      platform: platform,
      generatedAt: new Date().toISOString(),
      version: '1.0.0'
    }

    return JSON.stringify(appInfo, null, 2)
  }

  const showDownloadSuccess = (platform) => {
    const platformName = platform === 'android' ? 'Android APK' : 'iOS IPA'

    // Create a temporary success notification
    const notification = document.createElement('div')
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 animate-slide-in'
    notification.innerHTML = `
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        ${platformName} downloaded successfully!
      </div>
    `

    document.body.appendChild(notification)

    setTimeout(() => {
      notification.remove()
    }, 3000)
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-blue-600 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Preparing Your Downloads
          </h2>
          <p className="text-lg text-gray-600">
            Finalizing your mobile applications...
          </p>
        </div>

        <div className="card">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-green-600" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M17.523 15.3414c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993.0001.5511-.4482.9997-.9993.9997m-11.046 0c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993 0 .5511-.4482.9997-.9993.9997m11.4045-6.02l1.9973-3.4592a.416.416 0 00-.1518-.5972.416.416 0 00-.5972.1518l-2.0223 3.5024c-.5665-.2647-1.2014-.4132-1.8729-.4132-.6715 0-1.3064.1485-1.8729.4132L10.3936 5.8768a.4162.4162 0 00-.5972-.1518.4162.4162 0 00-.1518.5972L11.6419 9.321C9.8919 10.2599 8.7362 12.1314 8.7362 14.2819v1.9184h14.5276v-1.9184c0-2.1505-1.1557-4.022-2.9057-4.9609z"/>
                  </svg>
                </div>
                <span className="font-medium">Android APK</span>
              </div>
              <div className="animate-pulse w-16 h-2 bg-gray-300 rounded"></div>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-gray-600" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 ********** 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                  </svg>
                </div>
                <span className="font-medium">iOS IPA</span>
              </div>
              <div className="animate-pulse w-16 h-2 bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Download Preparation Failed
          </h2>
          <p className="text-lg text-gray-600 mb-6">
            {error}
          </p>
          <button onClick={() => window.location.reload()} className="btn-primary">
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce">
          <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          🎉 Your Apps Are Ready!
        </h2>
        <p className="text-lg text-gray-600">
          Download your mobile applications and start using them right away
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* Android Download */}
        <div className="card">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
              <svg className="w-8 h-8 text-green-600" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17.523 15.3414c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993.0001.5511-.4482.9997-.9993.9997m-11.046 0c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993 0 .5511-.4482.9997-.9993.9997m11.4045-6.02l1.9973-3.4592a.416.416 0 00-.1518-.5972.416.416 0 00-.5972.1518l-2.0223 3.5024c-.5665-.2647-1.2014-.4132-1.8729-.4132-.6715 0-1.3064.1485-1.8729.4132L10.3936 5.8768a.4162.4162 0 00-.5972-.1518.4162.4162 0 00-.1518.5972L11.6419 9.321C9.8919 10.2599 8.7362 12.1314 8.7362 14.2819v1.9184h14.5276v-1.9184c0-2.1505-1.1557-4.022-2.9057-4.9609z"/>
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Android App</h3>
              <p className="text-sm text-gray-500">APK file for Android devices</p>
            </div>
          </div>
          
          {downloadInfo?.android ? (
            <div className="space-y-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">File Size:</span>
                <span className="font-medium">{formatFileSize(downloadInfo.android.size)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Status:</span>
                <span className="text-green-600 font-medium">Ready</span>
              </div>
              <button
                onClick={() => handleDownload('android')}
                disabled={downloading.android}
                className="w-full btn-primary py-3 disabled:opacity-50"
              >
                {downloading.android ? (
                  <div className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Downloading...
                  </div>
                ) : (
                  <>
                    <svg className="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Download Android APK
                  </>
                )}
              </button>
            </div>
          ) : (
            <div className="text-center py-4">
              <div className="animate-spin w-6 h-6 border-2 border-primary-600 border-t-transparent rounded-full mx-auto mb-2"></div>
              <p className="text-sm text-gray-500">Preparing download...</p>
            </div>
          )}
        </div>

        {/* iOS Download */}
        <div className="card">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
              <svg className="w-8 h-8 text-gray-600" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 ********** 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">iOS App</h3>
              <p className="text-sm text-gray-500">IPA file for iOS devices</p>
            </div>
          </div>
          
          {downloadInfo?.ios ? (
            <div className="space-y-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">File Size:</span>
                <span className="font-medium">{formatFileSize(downloadInfo.ios.size)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Status:</span>
                <span className="text-green-600 font-medium">Ready</span>
              </div>
              <button
                onClick={() => handleDownload('ios')}
                disabled={downloading.ios}
                className="w-full btn-primary py-3 disabled:opacity-50"
              >
                {downloading.ios ? (
                  <div className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Downloading...
                  </div>
                ) : (
                  <>
                    <svg className="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Download iOS IPA
                  </>
                )}
              </button>
            </div>
          ) : (
            <div className="text-center py-4">
              <div className="animate-spin w-6 h-6 border-2 border-primary-600 border-t-transparent rounded-full mx-auto mb-2"></div>
              <p className="text-sm text-gray-500">Preparing download...</p>
            </div>
          )}
        </div>
      </div>

      {/* Installation Instructions */}
      <div className="card mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Installation Instructions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Android (APK)</h4>
            <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
              <li>Download the APK file to your Android device</li>
              <li>Enable "Install from unknown sources" in Settings</li>
              <li>Open the downloaded APK file</li>
              <li>Follow the installation prompts</li>
            </ol>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">iOS (IPA)</h4>
            <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
              <li>Download the IPA file to your computer</li>
              <li>Use Xcode or third-party tools to install</li>
              <li>For testing, use TestFlight or similar services</li>
              <li>Note: Requires developer certificate for distribution</li>
            </ol>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <button
          onClick={onReset}
          className="btn-secondary px-8 py-3"
        >
          Create Another App
        </button>
        <button
          onClick={() => window.location.href = '#support'}
          className="btn-primary px-8 py-3"
        >
          Get Support
        </button>
      </div>
    </div>
  )
}

export default DownloadSection
