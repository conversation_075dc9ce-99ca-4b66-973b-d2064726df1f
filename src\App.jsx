import React, { useState } from 'react'
import Header from './components/Header'
import URLInput from './components/URLInput'
import DesignCustomizer from './components/DesignCustomizer'
import AppPreview from './components/AppPreview'
import GenerationProgress from './components/GenerationProgress'
import DownloadSection from './components/DownloadSection'
import Footer from './components/Footer'

function App() {
  const [currentStep, setCurrentStep] = useState(1)
  const [appData, setAppData] = useState({
    url: '',
    title: '',
    description: '',
    icon: null,
    theme: 'modern',
    layout: 'standard',
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b'
    }
  })
  const [generationProgress, setGenerationProgress] = useState(0)
  const [isGenerating, setIsGenerating] = useState(false)
  const [downloadLinks, setDownloadLinks] = useState(null)

  const steps = [
    { id: 1, name: 'URL Input', description: 'Enter website URL' },
    { id: 2, name: 'Customize', description: 'Design your app' },
    { id: 3, name: 'Preview', description: 'Review your app' },
    { id: 4, name: 'Generate', description: 'Create app files' },
    { id: 5, name: 'Download', description: 'Get your apps' }
  ]

  const handleURLSubmit = (urlData) => {
    setAppData(prev => ({ ...prev, ...urlData }))
    setCurrentStep(2)
  }

  const handleDesignUpdate = (designData) => {
    setAppData(prev => ({ ...prev, ...designData }))
  }

  const handlePreviewConfirm = () => {
    setCurrentStep(4)
    startGeneration()
  }

  const startGeneration = async () => {
    setIsGenerating(true)
    setGenerationProgress(0)
    
    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return prev + Math.random() * 10
        })
      }, 500)

      // Call the actual generation API
      const response = await fetch('/api/generate-app', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(appData),
      })

      if (response.ok) {
        const result = await response.json()
        setDownloadLinks(result.downloadLinks)
        setGenerationProgress(100)
        setCurrentStep(5)
      } else {
        throw new Error('Generation failed')
      }
    } catch (error) {
      console.error('Generation error:', error)
      // Handle error state
    } finally {
      setIsGenerating(false)
    }
  }

  const resetApp = () => {
    setCurrentStep(1)
    setAppData({
      url: '',
      title: '',
      description: '',
      icon: null,
      theme: 'modern',
      layout: 'standard',
      colors: {
        primary: '#3b82f6',
        secondary: '#64748b'
      }
    })
    setGenerationProgress(0)
    setIsGenerating(false)
    setDownloadLinks(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex justify-center">
            <div className="flex items-center space-x-4">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    currentStep >= step.id 
                      ? 'bg-primary-600 border-primary-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-500'
                  }`}>
                    {currentStep > step.id ? (
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      step.id
                    )}
                  </div>
                  <div className="ml-2 hidden sm:block">
                    <p className="text-sm font-medium text-gray-900">{step.name}</p>
                    <p className="text-xs text-gray-500">{step.description}</p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-8 h-0.5 mx-4 ${
                      currentStep > step.id ? 'bg-primary-600' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Step Content */}
        <div className="max-w-4xl mx-auto">
          {currentStep === 1 && (
            <URLInput onSubmit={handleURLSubmit} />
          )}
          
          {currentStep === 2 && (
            <DesignCustomizer 
              appData={appData}
              onUpdate={handleDesignUpdate}
              onNext={() => setCurrentStep(3)}
              onBack={() => setCurrentStep(1)}
            />
          )}
          
          {currentStep === 3 && (
            <AppPreview 
              appData={appData}
              onConfirm={handlePreviewConfirm}
              onBack={() => setCurrentStep(2)}
            />
          )}
          
          {currentStep === 4 && (
            <GenerationProgress 
              progress={generationProgress}
              isGenerating={isGenerating}
            />
          )}
          
          {currentStep === 5 && (
            <DownloadSection 
              downloadLinks={downloadLinks}
              appData={appData}
              onReset={resetApp}
            />
          )}
        </div>
      </main>
      
      <Footer />
    </div>
  )
}

export default App
