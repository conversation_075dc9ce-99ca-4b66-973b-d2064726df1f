import React, { useState } from 'react'
import Header from './components/Header'
import URLInput from './components/URLInput'
import DesignCustomizer from './components/DesignCustomizer'
import AppPreview from './components/AppPreview'
import GenerationProgress from './components/GenerationProgress'
import DownloadSection from './components/DownloadSection'
import Footer from './components/Footer'
import StepIndicator from './components/StepIndicator'

function App() {
  const [currentStep, setCurrentStep] = useState(1)
  const [appData, setAppData] = useState({
    url: '',
    title: '',
    description: '',
    icon: null,
    theme: 'modern',
    layout: 'standard',
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b'
    }
  })
  const [generationProgress, setGenerationProgress] = useState(0)
  const [isGenerating, setIsGenerating] = useState(false)
  const [downloadLinks, setDownloadLinks] = useState(null)

  const steps = [
    { id: 1, name: 'URL Input', description: 'Enter website URL' },
    { id: 2, name: 'Customize', description: 'Design your app' },
    { id: 3, name: 'Preview', description: 'Review your app' },
    { id: 4, name: 'Generate', description: 'Create app files' },
    { id: 5, name: 'Download', description: 'Get your apps' }
  ]

  const handleURLSubmit = (urlData) => {
    setAppData(prev => ({ ...prev, ...urlData }))
    setCurrentStep(2)
  }

  const handleDesignUpdate = (designData) => {
    setAppData(prev => ({ ...prev, ...designData }))
  }

  const handlePreviewConfirm = () => {
    setCurrentStep(4)
    startGeneration()
  }

  const startGeneration = async () => {
    setIsGenerating(true)
    setGenerationProgress(0)

    try {
      // Simulate realistic progress updates
      const progressSteps = [
        { progress: 10, delay: 500, message: 'Analyzing website...' },
        { progress: 25, delay: 1000, message: 'Processing assets...' },
        { progress: 45, delay: 1500, message: 'Building Android app...' },
        { progress: 70, delay: 2000, message: 'Building iOS app...' },
        { progress: 90, delay: 1000, message: 'Finalizing...' },
        { progress: 100, delay: 500, message: 'Complete!' }
      ]

      for (const step of progressSteps) {
        await new Promise(resolve => setTimeout(resolve, step.delay))
        setGenerationProgress(step.progress)
      }

      // Simulate API call
      try {
        const response = await fetch('/api/generate/app', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(appData),
        })

        if (response.ok) {
          const result = await response.json()
          setDownloadLinks(result.downloadLinks || { generationId: `gen_${Date.now()}` })
        } else {
          // Fallback to mock data
          setDownloadLinks({ generationId: `gen_${Date.now()}` })
        }
      } catch (apiError) {
        console.log('API not available, using mock data')
        // Create mock download links
        setDownloadLinks({ generationId: `gen_${Date.now()}` })
      }

      setCurrentStep(5)
    } catch (error) {
      console.error('Generation error:', error)
      alert('Generation failed. Please try again.')
      setCurrentStep(3) // Go back to preview
    } finally {
      setIsGenerating(false)
    }
  }

  const resetApp = () => {
    setCurrentStep(1)
    setAppData({
      url: '',
      title: '',
      description: '',
      icon: null,
      theme: 'modern',
      layout: 'standard',
      colors: {
        primary: '#3b82f6',
        secondary: '#64748b'
      }
    })
    setGenerationProgress(0)
    setIsGenerating(false)
    setDownloadLinks(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Progress Steps */}
        <StepIndicator steps={steps} currentStep={currentStep} />

        {/* Step Content */}
        <div className="max-w-4xl mx-auto">
          {currentStep === 1 && (
            <URLInput onSubmit={handleURLSubmit} />
          )}
          
          {currentStep === 2 && (
            <DesignCustomizer 
              appData={appData}
              onUpdate={handleDesignUpdate}
              onNext={() => setCurrentStep(3)}
              onBack={() => setCurrentStep(1)}
            />
          )}
          
          {currentStep === 3 && (
            <AppPreview 
              appData={appData}
              onConfirm={handlePreviewConfirm}
              onBack={() => setCurrentStep(2)}
            />
          )}
          
          {currentStep === 4 && (
            <GenerationProgress 
              progress={generationProgress}
              isGenerating={isGenerating}
            />
          )}
          
          {currentStep === 5 && (
            <DownloadSection 
              downloadLinks={downloadLinks}
              appData={appData}
              onReset={resetApp}
            />
          )}
        </div>
      </main>
      
      <Footer />
    </div>
  )
}

export default App
