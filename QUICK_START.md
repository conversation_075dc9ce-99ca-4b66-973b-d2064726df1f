# Quick Start Guide ⚡

Get URL to App PWA running in 5 minutes!

## 🚀 One-Command Setup

```bash
# Clone, install, and start
git clone https://github.com/your-username/url-to-app-pwa.git && \
cd url-to-app-pwa && \
npm install && \
cp .env.example .env && \
npm run dev
```

## 📱 Access Your App

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000/api
- **Health Check**: http://localhost:5000/api/health

## 🎯 Test the App

1. **Enter a URL**: Try `https://example.com`
2. **Customize Design**: Choose theme and colors
3. **Preview**: See how your app will look
4. **Generate**: Create Android APK and iOS IPA files
5. **Download**: Get your mobile apps!

## 🐳 Docker Quick Start

```bash
# Using Docker Compose
docker-compose up -d

# Or using Docker directly
docker build -t url-to-app . && \
docker run -p 5000:5000 url-to-app
```

## 🔧 Environment Variables

Key settings in `.env`:

```env
NODE_ENV=development
PORT=5000
CORS_ORIGIN=http://localhost:3000
MAX_CONCURRENT_GENERATIONS=5
```

## 📋 Requirements

- Node.js 18+
- 4GB+ RAM
- 10GB+ free space

## 🆘 Troubleshooting

**Port already in use?**
```bash
# Kill process on port 5000
npx kill-port 5000
```

**Puppeteer issues?**
```bash
# Install dependencies (Ubuntu/Debian)
sudo apt-get install -y chromium-browser
```

**Memory issues?**
```bash
# Reduce concurrent generations
echo "MAX_CONCURRENT_GENERATIONS=2" >> .env
```

## 📞 Need Help?

- 📖 [Full Documentation](README.md)
- 🚀 [Deployment Guide](DEPLOYMENT.md)
- 🐛 [Report Issues](https://github.com/your-username/url-to-app-pwa/issues)

---

Happy app generating! 🎉
