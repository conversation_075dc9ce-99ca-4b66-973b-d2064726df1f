const express = require('express')
const { body, validationResult } = require('express-validator')
const AppGenerator = require('../services/AppGenerator')
const WebsiteAnalyzer = require('../services/WebsiteAnalyzer')

const router = express.Router()

// Validation middleware
const validateAppGeneration = [
  body('url').isURL().withMessage('Valid URL is required'),
  body('title').isLength({ min: 1, max: 50 }).withMessage('Title must be 1-50 characters'),
  body('description').optional().isLength({ max: 200 }).withMessage('Description must be less than 200 characters'),
  body('theme').isIn(['modern', 'classic', 'minimal', 'dark']).withMessage('Invalid theme'),
  body('layout').isIn(['standard', 'tabbed', 'drawer']).withMessage('Invalid layout'),
  body('colors.primary').isHexColor().withMessage('Invalid primary color'),
  body('colors.secondary').isHexColor().withMessage('Invalid secondary color')
]

// Generate app endpoint
router.post('/app', validateAppGeneration, async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: errors.array() 
      })
    }

    const { url, title, description, theme, layout, colors, icon } = req.body

    // Analyze the website first
    const websiteAnalyzer = new WebsiteAnalyzer()
    const websiteData = await websiteAnalyzer.analyze(url)

    // Generate the app
    const appGenerator = new AppGenerator()
    const generationResult = await appGenerator.generateApp({
      url,
      title,
      description,
      theme,
      layout,
      colors,
      icon,
      websiteData
    })

    res.json({
      success: true,
      generationId: generationResult.id,
      downloadLinks: generationResult.downloadLinks,
      message: 'App generated successfully'
    })

  } catch (error) {
    console.error('App generation error:', error)
    res.status(500).json({ 
      error: 'App generation failed', 
      message: error.message 
    })
  }
})

// Get generation status
router.get('/status/:generationId', async (req, res) => {
  try {
    const { generationId } = req.params
    const appGenerator = new AppGenerator()
    const status = await appGenerator.getGenerationStatus(generationId)
    
    res.json(status)
  } catch (error) {
    console.error('Status check error:', error)
    res.status(500).json({ 
      error: 'Failed to get generation status', 
      message: error.message 
    })
  }
})

module.exports = router
